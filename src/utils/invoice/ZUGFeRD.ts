// Kontext des Dokuments (Version und Teststatus)

import { KindOfInvoice } from "@prisma/client";
import { ISO3166_country_codes } from "~/utils/format/countrycodes";
import * as he from "he";
import {InvoiceWithIncludes} from "~/utils/invoice/createInvoicePDF";
import {getContactAdressByDate} from "~/utils/contact/getContactAdressByDate";

// Informationen zum Rechnungsdokument
export interface ExchangedDocumentData {
  invoiceNumber: string; // Rechnungsnummer
  invoiceTypeCode: string; // Rechnungsart (z.B. "380" für normale Rechnung)
  invoiceDate: string; // Rechnungsdatum (Format: YYYYMMDD)
  invoiceName: string; // Bezeichnung des Dokuments (z.B. "RECHNUNG")
}

// Partei (z.B. Verkäufer oder Käufer)
export interface Party {
  name: string; // Name der Partei
  street: string; // Straße der Partei
  streetNumber?: string; // Hausnummer (optional)
  zipcode: string; // Postleitzahl
  city: string; // Stadt
  country: string; // Länderkürzel (z.B. "DE")
  vatId?: string;
}

// Daten zu den Geschäftspartnern
export interface HeaderTradeAgreementData {
  seller: Party; // Verkäufer-Daten
  buyer: Party; // Käufer-Daten
  buyerReference: string; // Käufer-Referenz (optional Bestellnummer)
}

// Zahlungs- und Steuerinformationen
export interface HeaderTradeSettlementData {
  currency: string; // Währung der Rechnung (z.B. "EUR")
  taxRate: number; // Steuersatz (z.B. 19 für 19%)
  netTotalAmount: number; // Gesamtnetto-Betrag der Rechnung
  taxTotalAmount: number; // Steuerbetrag der Rechnung
  grandTotalAmount: number; // Bruttosumme der Rechnung
  iban: string; // IBAN des Zahlungsempfängers
  bic: string; // BIC des Zahlungsempfängers
  billingPeriodStart: string;
  billingPeriodEnd: string;
}

// Einzelne Rechnungspositionen
export interface TradeLineItemData {
  lineId: string; // Positionsnummer
  description: string; // Beschreibung der Position
  quantity: number; // Menge
  unitCode: string; // Einheit (z.B. "C62" für Stück, KWH)
  unitPrice: number; // Einzelpreis netto
  taxPercent: number; // Steuersatz für die Position
  lineTotalAmount: number; // Gesamtbetrag netto der Position
}

const getUnitCode = (unit: string): string => {
  return UNIT_CODE[unit as keyof typeof UNIT_CODE] || "UNKNOWN";
};

enum UNIT_CODE {
  kWh = "KWH",
  stk = "C62",
  session = "E48",
  minute = "MIN",
  min = "MIN",
  Pauschal = "C62",
}

export const createExchangedDocumentContext = (): string => `
 <rsm:ExchangedDocumentContext>
    <ram:GuidelineSpecifiedDocumentContextParameter>
        <ram:ID>urn:cen.eu:en16931:2017</ram:ID>
    </ram:GuidelineSpecifiedDocumentContextParameter>
 </rsm:ExchangedDocumentContext>`;

export const createExchangedDocument = (data: ExchangedDocumentData): string => `
<rsm:ExchangedDocument xmlns:ram="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100" xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100">
    <ram:ID>${data.invoiceNumber}</ram:ID>
    <ram:TypeCode>${data.invoiceTypeCode}</ram:TypeCode>
    <ram:IssueDateTime>
        <udt:DateTimeString format="102">${data.invoiceDate}</udt:DateTimeString>
    </ram:IssueDateTime>
    <ram:IncludedNote>   
        <ram:Content>${data.invoiceName}</ram:Content>
    </ram:IncludedNote>
</rsm:ExchangedDocument>`;

export const createApplicableHeaderTradeAgreement = (data: HeaderTradeAgreementData): string => `
<ram:ApplicableHeaderTradeAgreement>
    <ram:SellerTradeParty>
        <ram:Name>${data.seller.name}</ram:Name>
        <ram:PostalTradeAddress>
            <ram:PostcodeCode>${data.seller.zipcode}</ram:PostcodeCode>
            <ram:LineOne>${he.escape(data.seller.street)} ${data.seller.streetNumber}</ram:LineOne>
            <ram:CityName>${data.seller.city}</ram:CityName>
            <ram:CountryID>${ISO3166_country_codes[data.seller.country]}</ram:CountryID>
        </ram:PostalTradeAddress>
        <ram:SpecifiedTaxRegistration>
            <ram:ID schemeID="VA">${data.seller.vatId}</ram:ID>
        </ram:SpecifiedTaxRegistration>
    </ram:SellerTradeParty>
    <ram:BuyerTradeParty>
        <ram:Name>${he.escape(data.buyer.name)}</ram:Name>
        <ram:PostalTradeAddress>
            <ram:PostcodeCode>${data.buyer.zipcode}</ram:PostcodeCode>
            <ram:LineOne>${escape(data.buyer.street)} ${data.buyer.streetNumber}</ram:LineOne>
            <ram:CityName>${data.buyer.city}</ram:CityName>
            <ram:CountryID>${ISO3166_country_codes[data.buyer.country]}</ram:CountryID>
        </ram:PostalTradeAddress>
        <ram:SpecifiedTaxRegistration>
            <ram:ID schemeID="VA">${data.buyer.vatId}</ram:ID>
        </ram:SpecifiedTaxRegistration>
    </ram:BuyerTradeParty>
</ram:ApplicableHeaderTradeAgreement>`;

export const createApplicableHeaderTradeSettlement = (data: HeaderTradeSettlementData): string => `
 <ram:ApplicableHeaderTradeSettlement>
    <ram:InvoiceCurrencyCode>${data.currency}</ram:InvoiceCurrencyCode>
    <ram:ApplicableTradeTax>
        <ram:CalculatedAmount>${data.taxTotalAmount}</ram:CalculatedAmount>
        <ram:TypeCode>VAT</ram:TypeCode>
        <ram:BasisAmount>${data.netTotalAmount}</ram:BasisAmount>
        <ram:CategoryCode>${data.taxRate == 0 ? "AE" : "S"}</ram:CategoryCode>
          ${
            data.taxRate == 0
              ? "<ram:ExemptionReasonCode>VATEX-EU-AE</ram:ExemptionReasonCode>"
              : ""
          }
        <ram:RateApplicablePercent>${data.taxRate}</ram:RateApplicablePercent>
      
   </ram:ApplicableTradeTax>
    <ram:BillingSpecifiedPeriod>
        <ram:StartDateTime>
            <udt:DateTimeString format="102">${data.billingPeriodStart}</udt:DateTimeString>
        </ram:StartDateTime>
        <ram:EndDateTime>
            <udt:DateTimeString format="102">${data.billingPeriodEnd}</udt:DateTimeString>
        </ram:EndDateTime>
    </ram:BillingSpecifiedPeriod>
    <ram:SpecifiedTradePaymentTerms>
        <ram:Description>14 days</ram:Description>
    </ram:SpecifiedTradePaymentTerms>
    <ram:SpecifiedTradeSettlementHeaderMonetarySummation>
        <ram:LineTotalAmount>${data.netTotalAmount}</ram:LineTotalAmount>
        <ram:ChargeTotalAmount>0.00</ram:ChargeTotalAmount>
        <ram:AllowanceTotalAmount>0.00</ram:AllowanceTotalAmount>
        <ram:TaxBasisTotalAmount>${data.netTotalAmount}</ram:TaxBasisTotalAmount>
        <ram:TaxTotalAmount currencyID="EUR">${data.taxTotalAmount}</ram:TaxTotalAmount>
        <ram:GrandTotalAmount>${data.grandTotalAmount}</ram:GrandTotalAmount>
        <ram:TotalPrepaidAmount>0.00</ram:TotalPrepaidAmount>
        <ram:DuePayableAmount>${data.grandTotalAmount}</ram:DuePayableAmount>
    </ram:SpecifiedTradeSettlementHeaderMonetarySummation>

   </ram:ApplicableHeaderTradeSettlement>`;

export const createIncludedSupplyChainTradeLineItem = (data: TradeLineItemData): string => `
<ram:IncludedSupplyChainTradeLineItem>
    <ram:AssociatedDocumentLineDocument>
        <ram:LineID>${data.lineId}</ram:LineID>
    </ram:AssociatedDocumentLineDocument>
     <ram:SpecifiedTradeProduct>
        <ram:Name>${data.description}</ram:Name>
     </ram:SpecifiedTradeProduct>
      <ram:SpecifiedLineTradeAgreement>
            <ram:GrossPriceProductTradePrice>
                <ram:ChargeAmount>${(data.unitPrice * (1 + data.taxPercent / 100)).toFixed(
                  2,
                )}</ram:ChargeAmount>
            </ram:GrossPriceProductTradePrice>
            <ram:NetPriceProductTradePrice>
                <ram:ChargeAmount>${data.unitPrice.toFixed(2)}</ram:ChargeAmount>
            </ram:NetPriceProductTradePrice>
      </ram:SpecifiedLineTradeAgreement>       
    <ram:SpecifiedLineTradeDelivery>
        <ram:BilledQuantity unitCode="${data.unitCode}">${data.quantity}</ram:BilledQuantity>
    </ram:SpecifiedLineTradeDelivery>
    <ram:SpecifiedLineTradeSettlement>
        <ram:ApplicableTradeTax>
            <ram:TypeCode>VAT</ram:TypeCode>
             <ram:CategoryCode>${data.taxPercent == 0 ? "AE" : "S"}</ram:CategoryCode>
            <ram:RateApplicablePercent>${data.taxPercent.toFixed(2)}</ram:RateApplicablePercent>
        </ram:ApplicableTradeTax>
        <ram:SpecifiedTradeSettlementLineMonetarySummation>
            <ram:LineTotalAmount>${data.lineTotalAmount.toFixed(2)}</ram:LineTotalAmount>
        </ram:SpecifiedTradeSettlementLineMonetarySummation>
    </ram:SpecifiedLineTradeSettlement>
</ram:IncludedSupplyChainTradeLineItem>`;

const formatKindOfInvoice = (invoice: InvoiceWithIncludes) => {
  switch (invoice.kindOfInvoice) {
    case KindOfInvoice.INVOICE:
      return "380"; // wenn lastschrift dann 383
    case KindOfInvoice.CREDIT:
      return "381";
    case KindOfInvoice.STORNO:
      return "389";
    case KindOfInvoice.CREDIT_STORNO:
      return "381"; // wie eine gutschrift
    default:
      return "380";
  }
};

const formatDate = (date: Date | undefined | null) => {
  if (!date) {
    return "fehler";
  }
  let formatedDate = date.toISOString().split("T")[0] ?? "";
  formatedDate = formatedDate.replace(/-/g, "");
  return formatedDate;
};

// Hauptfunktion zur Erstellung des finalen ZUGFeRD-XML
export const createFinalZUGFeRDXML = (invoice: InvoiceWithIncludes): string => {
  let contactAddress;

  if (invoice?.contact && invoice.invoiceDate) {
    contactAddress = getContactAdressByDate(invoice.contact.contactAddress, invoice.invoiceDate);
  } else if (invoice?.user && invoice.invoiceDate) {
    contactAddress = getContactAdressByDate(invoice.user.address, invoice.invoiceDate);
  } else {
    throw new Error("kein contact");
  }

  // ExchangedDocumentContext-Daten
  const exchangedDocumentContextXML = createExchangedDocumentContext();

  // ExchangedDocument-Daten
  const exchangedDocumentXML = createExchangedDocument({
    invoiceNumber: invoice.invoiceNumber || "INV-12345",
    invoiceTypeCode: formatKindOfInvoice(invoice),
    invoiceDate: formatDate(invoice.invoiceDate),
    invoiceName: invoice.kindOfInvoice,
  });

  // ApplicableHeaderTradeAgreement-Daten
  const headerTradeAgreementXML = createApplicableHeaderTradeAgreement({
    seller: {
      name: "Eulektro GmbH",
      street: "Werderstraße",
      streetNumber: "69",
      zipcode: "28199",
      city: "Bremen",
      country: "Deutschland",
      vatId: "DE343815692",
    },
    buyer: {
      name: invoice.contact?.companyName || "fehler",
      street: contactAddress?.street || "fehler",
      streetNumber: contactAddress?.streetNr || "fehler",
      zipcode: contactAddress?.zip || "fehler",
      city: contactAddress?.city || "fehler",
      country: contactAddress?.country || "fehler", //todo aufschlüsseln
      vatId: contactAddress?.ustId ?? "",
    },
    buyerReference: invoice.invoiceNumber ?? "", // Optional, kannst du anpassen
  });

  // ApplicableHeaderTradeSettlement-Daten (Zahlungsinformationen)
  const headerTradeSettlementXML = createApplicableHeaderTradeSettlement({
    currency: "EUR",
    taxRate: invoice.sumTax > 0 ? 19 : 0,
    netTotalAmount: invoice.sumNet,
    taxTotalAmount: invoice.sumTax,
    grandTotalAmount: invoice.sumGross,
    iban: "**********************",
    bic: "QNTODEB2XXX",
    billingPeriodStart: formatDate(invoice.servicePeriodFrom),
    billingPeriodEnd: formatDate(invoice.servicePeriodTo),
  });

  // IncludedSupplyChainTradeLineItem-Daten (Rechnungspositionen)
  const lineItemsXML = invoice.invoicePositions
    .map((position, index) =>
      createIncludedSupplyChainTradeLineItem({
        lineId: position.pos?.toString() ?? "fehler",
        description: position.description || "Position",
        quantity: position.amount,
        unitCode: getUnitCode(position?.unit ?? "stk"), // Stück als Default-Einheit
        unitPrice: position.unitPrice,
        taxPercent: position.taxRate,
        lineTotalAmount: position.sumNet,
      }),
    )
    .join("");

  // Zusammenführung aller Abschnitte zu einem finalen XML
  return `
<?xml version="1.0" encoding="UTF-8"?>
<rsm:CrossIndustryInvoice xmlns:a="urn:un:unece:uncefact:data:standard:QualifiedDataType:100" xmlns:rsm="urn:un:unece:uncefact:data:standard:CrossIndustryInvoice:100" xmlns:qdt="urn:un:unece:uncefact:data:standard:QualifiedDataType:10" xmlns:ram="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100">
    ${exchangedDocumentContextXML}
    ${exchangedDocumentXML}
    <rsm:SupplyChainTradeTransaction>
        ${lineItemsXML}
        ${headerTradeAgreementXML}
        <ram:ApplicableHeaderTradeDelivery>
        </ram:ApplicableHeaderTradeDelivery>
        ${headerTradeSettlementXML}
    </rsm:SupplyChainTradeTransaction>
</rsm:CrossIndustryInvoice>
  `.trim();
};

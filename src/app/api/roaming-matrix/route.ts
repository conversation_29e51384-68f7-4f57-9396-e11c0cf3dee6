import prisma from "~/server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";

export async function GET() {
  const session = await getServerSession(authOptions);
  const user = session?.user;

  if (!session || !user) {
    return NextResponse.json("No Login", { status: 401 });
  }

  // Check if user has permission to view roaming matrix
  if (!user.role || ![Role.ADMIN, Role.CPO, Role.CARD_MANAGER].includes(user.role)) {
    return NextResponse.json("Insufficient permissions", { status: 403 });
  }

  try {
    // Get current date for validity check
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Fetch contacts with visibleInRoamingMatrix flag and their tariffs
    const contacts = await prisma.contact.findMany({
      where: {
        visibleInRoamingMatrix: true,
      },
      include: {
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
        tarifs: {
          include: {
            tarif: {
              include: {
                validOus: {
                  select: {
                    id: true,
                    name: true,
                    code: true,
                  },
                },
              },
            },
          },
        },
        creditTarifs: {
          include: {
            creditTarif: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Process contacts to include only valid tariffs
    const roamingMatrixData = contacts.map((contact) => {
      // Filter valid roaming tariffs (Tarif model)
      const allValidRoamingTariffs = contact.tarifs
        .map((tarifOnContact) => tarifOnContact.tarif)
        .filter((tarif) => {
          const validFrom = new Date(tarif.validFrom);
          const validTo = new Date(tarif.validTo);
          return validFrom <= today && validTo >= today;
        });

      // Check for multiple valid tariffs per currentType and throw error
      const acTariffs = allValidRoamingTariffs.filter(t => t.currentType === "AC");
      const dcTariffs = allValidRoamingTariffs.filter(t => t.currentType === "DC");

      // Throw error if multiple tariffs exist for the same currentType
      if (acTariffs.length > 1) {
        const tariffNames = acTariffs.map(t => t.name).join(", ");
        throw new Error(`Contact "${contact.companyName || contact.name}" has multiple valid AC tariffs: ${tariffNames}. Please ensure only one tariff per currentType is valid at any time.`);
      }

      if (dcTariffs.length > 1) {
        const tariffNames = dcTariffs.map(t => t.name).join(", ");
        throw new Error(`Contact "${contact.companyName || contact.name}" has multiple valid DC tariffs: ${tariffNames}. Please ensure only one tariff per currentType is valid at any time.`);
      }

      // Use the single valid tariff per type
      const validRoamingTariffs = [...acTariffs, ...dcTariffs];

      // Filter valid credit tariffs (CreditTarif model)
      const validCreditTariffs = contact.creditTarifs
        .map((creditTarifOnContact) => creditTarifOnContact.creditTarif)
        .filter((creditTarif) => {
          const validFrom = new Date(creditTarif.validFrom);
          const validTo = creditTarif.validTo ? new Date(creditTarif.validTo) : null;
          return validFrom <= today && (!validTo || validTo >= today);
        });

      return {
        id: contact.id,
        name: contact.name,
        companyName: contact.companyName,
        customerNumber: contact.customerNumber,
        supplierNumber: contact.supplierNumber,
        cpo: contact.cpo,
        noRoaming: contact.noRoaming,
        ou: contact.ou,
        acTariff: acTariffs[0] ? {
          id: acTariffs[0].id,
          name: acTariffs[0].name,
          kwh: acTariffs[0].kwh,
          sessionFee: acTariffs[0].sessionFee,
          kindOfTarif: acTariffs[0].kindOfTarif,
          validFrom: acTariffs[0].validFrom,
          validTo: acTariffs[0].validTo,
          validOus: acTariffs[0].validOus,
        } : null,
        dcTariff: dcTariffs[0] ? {
          id: dcTariffs[0].id,
          name: dcTariffs[0].name,
          kwh: dcTariffs[0].kwh,
          sessionFee: dcTariffs[0].sessionFee,
          kindOfTarif: dcTariffs[0].kindOfTarif,
          validFrom: dcTariffs[0].validFrom,
          validTo: dcTariffs[0].validTo,
          validOus: dcTariffs[0].validOus,
        } : null,
        validCreditTariffs: validCreditTariffs.map((creditTarif) => ({
          id: creditTarif.id,
          name: creditTarif.name,
          tarifType: creditTarif.tarifType,
          powerType: creditTarif.powerType,
          sessionCredit: creditTarif.sessionCredit,
          energyCredit: creditTarif.energyCredit,
          kindOfTarif: creditTarif.kindOfTarif,
          validFrom: creditTarif.validFrom,
          validTo: creditTarif.validTo,
        })),
      };
    });

    return NextResponse.json(roamingMatrixData);
  } catch (error) {
    console.error("Error fetching roaming matrix data:", error);
    return NextResponse.json("Internal server error", { status: 500 });
  }
}

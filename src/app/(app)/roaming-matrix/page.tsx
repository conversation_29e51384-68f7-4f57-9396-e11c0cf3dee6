"use client";

import React, { useState, useEffect } from "react";
import { Chip } from "~/app/(app)/component/chip";

interface TariffData {
  id: string;
  name: string;
  kwh: number;
  sessionFee: number;
  minChargingTime: number;
  minChargingEnergy: number;
  blockingFee: number;
  blockingFeeBeginAtMin: number;
  blockingFeeMax: number;
  kindOfTarif: string;
  validFrom: string;
  validTo: string;
  validOus: Array<{
    id: string;
    name: string;
    code: string;
  }>;
}

interface RoamingMatrixData {
  id: string;
  name: string;
  companyName: string;
  customerNumber: string;
  supplierNumber: string;
  cpo: boolean;
  noRoaming: boolean;
  ou: {
    id: string;
    name: string;
    code: string;
  } | null;
  acTariff: TariffData | null;
  dcTariff: TariffData | null;
  validCreditTariffs: Array<{
    id: string;
    name: string;
    tarifType: string;
    powerType: string;
    sessionCredit: number;
    energyCredit: number;
    kindOfTarif: string;
    validFrom: string;
    validTo: string | null;
  }>;
}

const RoamingMatrixPage = () => {
  const [data, setData] = useState<RoamingMatrixData[]>([]);
  const [filteredData, setFilteredData] = useState<RoamingMatrixData[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    acTariff: "",
    dcTariff: "",
    ou: "",
    cpo: "all",
    noRoaming: "all"
  });

  useEffect(() => {
    fetchRoamingMatrixData();
  }, []);

  const fetchRoamingMatrixData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/roaming-matrix");
      if (response.ok) {
        const data = await response.json();
        setData(data);
        setFilteredData(data);
      } else {
        console.error("Failed to fetch roaming matrix data");
      }
    } catch (error) {
      console.error("Error fetching roaming matrix data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Filter data based on current filters
  useEffect(() => {
    let filtered = data;

    if (filters.acTariff) {
      filtered = filtered.filter(contact =>
        contact.acTariff?.name?.toLowerCase().includes(filters.acTariff.toLowerCase())
      );
    }

    if (filters.dcTariff) {
      filtered = filtered.filter(contact =>
        contact.dcTariff?.name?.toLowerCase().includes(filters.dcTariff.toLowerCase())
      );
    }

    if (filters.ou) {
      filtered = filtered.filter(contact =>
        contact.ou?.name?.toLowerCase().includes(filters.ou.toLowerCase())
      );
    }

    if (filters.cpo !== "all") {
      filtered = filtered.filter(contact =>
        filters.cpo === "true" ? contact.cpo : !contact.cpo
      );
    }

    if (filters.noRoaming !== "all") {
      filtered = filtered.filter(contact =>
        filters.noRoaming === "true" ? contact.noRoaming : !contact.noRoaming
      );
    }

    setFilteredData(filtered);
  }, [data, filters]);

  // Get unique values for filter dropdowns
  const getUniqueValues = () => {
    const acTariffs = [...new Set(data.map(c => c.acTariff?.name).filter(Boolean))];
    const dcTariffs = [...new Set(data.map(c => c.dcTariff?.name).filter(Boolean))];
    const ous = [...new Set(data.map(c => c.ou?.name).filter(Boolean))];

    return { acTariffs, dcTariffs, ous };
  };

  const { acTariffs: uniqueAcTariffs, dcTariffs: uniqueDcTariffs, ous: uniqueOus } = getUniqueValues();

  const ContactCard = ({ contact }: { contact: RoamingMatrixData }) => {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-4">
        {/* Contact Header */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {contact.companyName || contact.name || "Unbekannter Contact"}
            </h3>
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
              {contact.customerNumber && (
                <span>Kunde: {contact.customerNumber}</span>
              )}
              {contact.ou && (
                <span>OU: {contact.ou.name}</span>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {contact.cpo && (
              <Chip label="CPO" className="bg-purple-100 text-purple-800 text-xs" />
            )}
            {contact.noRoaming && (
              <Chip label="Kein Roaming" className="bg-red-100 text-red-800 text-xs" />
            )}
          </div>
        </div>

        {/* Tariff Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* AC Tariff */}
          <div className={`rounded-lg p-3 border-2 ${contact.acTariff
            ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-700'
            : 'bg-gray-50 border-gray-200 dark:bg-gray-700 dark:border-gray-600'
          }`}>
            <div className="flex items-center justify-between mb-3">
              <span className={`px-2 py-1 rounded text-sm font-medium ${contact.acTariff
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100'
              }`}>
                AC
              </span>
              {contact.acTariff && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {contact.acTariff.name}
                </span>
              )}
            </div>
            {contact.acTariff ? (
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">kWh:</span>
                  <span className="font-semibold">{contact.acTariff.kwh.toFixed(4)}€</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Session:</span>
                  <span className="font-semibold">{contact.acTariff.sessionFee?.toFixed(2) || '0.00'}€</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Min. Zeit:</span>
                  <span className="font-semibold">{contact.acTariff.minChargingTime}s</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Min. Energie:</span>
                  <span className="font-semibold">{contact.acTariff.minChargingEnergy}kWh</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Blockier ab:</span>
                  <span className="font-semibold">{contact.acTariff.blockingFeeBeginAtMin}min</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Blockiergebühr:</span>
                  <span className="font-semibold">{contact.acTariff.blockingFee.toFixed(4)}€/min</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Max. Blockier:</span>
                  <span className="font-semibold">{contact.acTariff.blockingFeeMax.toFixed(2)}€</span>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-400 py-4">
                Kein AC-Tarif
              </div>
            )}
          </div>

          {/* DC Tariff */}
          <div className={`rounded-lg p-3 border-2 ${contact.dcTariff
            ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700'
            : 'bg-gray-50 border-gray-200 dark:bg-gray-700 dark:border-gray-600'
          }`}>
            <div className="flex items-center justify-between mb-3">
              <span className={`px-2 py-1 rounded text-sm font-medium ${contact.dcTariff
                ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100'
              }`}>
                DC
              </span>
              {contact.dcTariff && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {contact.dcTariff.name}
                </span>
              )}
            </div>
            {contact.dcTariff ? (
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">kWh:</span>
                  <span className="font-semibold">{contact.dcTariff.kwh.toFixed(4)}€</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Session:</span>
                  <span className="font-semibold">{contact.dcTariff.sessionFee?.toFixed(2) || '0.00'}€</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Min. Zeit:</span>
                  <span className="font-semibold">{contact.dcTariff.minChargingTime}s</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Min. Energie:</span>
                  <span className="font-semibold">{contact.dcTariff.minChargingEnergy}kWh</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Blockier ab:</span>
                  <span className="font-semibold">{contact.dcTariff.blockingFeeBeginAtMin}min</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Blockiergebühr:</span>
                  <span className="font-semibold">{contact.dcTariff.blockingFee.toFixed(4)}€/min</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Max. Blockier:</span>
                  <span className="font-semibold">{contact.dcTariff.blockingFeeMax.toFixed(2)}€</span>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-400 py-4">
                Kein DC-Tarif
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };



  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Roaming Matrix
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Übersicht aller Contacts mit ihren gültigen zugeordneten Tarifen
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Roaming Matrix
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Übersicht aller Contacts mit ihren zugeordneten Tarifen
        </p>

        {/* Filter Section */}
        <div className="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Filter</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* AC Tariff Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                AC-Tarif
              </label>
              <select
                value={filters.acTariff}
                onChange={(e) => setFilters(prev => ({ ...prev, acTariff: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white text-sm"
              >
                <option value="">Alle AC-Tarife</option>
                {uniqueAcTariffs.map(tariff => (
                  <option key={tariff} value={tariff}>{tariff}</option>
                ))}
              </select>
            </div>

            {/* DC Tariff Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                DC-Tarif
              </label>
              <select
                value={filters.dcTariff}
                onChange={(e) => setFilters(prev => ({ ...prev, dcTariff: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white text-sm"
              >
                <option value="">Alle DC-Tarife</option>
                {uniqueDcTariffs.map(tariff => (
                  <option key={tariff} value={tariff}>{tariff}</option>
                ))}
              </select>
            </div>

            {/* OU Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                OU
              </label>
              <select
                value={filters.ou}
                onChange={(e) => setFilters(prev => ({ ...prev, ou: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white text-sm"
              >
                <option value="">Alle OUs</option>
                {uniqueOus.map(ou => (
                  <option key={ou} value={ou}>{ou}</option>
                ))}
              </select>
            </div>

            {/* CPO Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                CPO
              </label>
              <select
                value={filters.cpo}
                onChange={(e) => setFilters(prev => ({ ...prev, cpo: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white text-sm"
              >
                <option value="all">Alle</option>
                <option value="true">Nur CPO</option>
                <option value="false">Nur Nicht-CPO</option>
              </select>
            </div>

            {/* No Roaming Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Roaming
              </label>
              <select
                value={filters.noRoaming}
                onChange={(e) => setFilters(prev => ({ ...prev, noRoaming: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white text-sm"
              >
                <option value="all">Alle</option>
                <option value="false">Mit Roaming</option>
                <option value="true">Ohne Roaming</option>
              </select>
            </div>
          </div>

          {/* Clear Filters Button */}
          <div className="mt-4">
            <button
              onClick={() => setFilters({
                acTariff: "",
                dcTariff: "",
                ou: "",
                cpo: "all",
                noRoaming: "all"
              })}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Filter zurücksetzen
            </button>
          </div>
        </div>

        <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
          <span>{filteredData.length} von {data.length} Contacts angezeigt</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredData.length === 0 ? (
          <div className="col-span-full bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-8 text-center">
            <div className="text-gray-400 text-lg mb-2">🌐</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {data.length === 0 ? "Keine Contacts in der Roaming Matrix" : "Keine Contacts entsprechen den Filterkriterien"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {data.length === 0
                ? "Es wurden noch keine Contacts für die Roaming Matrix markiert. Bearbeiten Sie Contacts und setzen Sie das Flag 'Sichtbar in Roaming Matrix'."
                : "Versuchen Sie, die Filter anzupassen oder zurückzusetzen."
              }
            </p>
          </div>
        ) : (
          filteredData.map((contact) => (
            <ContactCard key={contact.id} contact={contact} />
          ))
        )}
      </div>
    </div>
  );
};

export default RoamingMatrixPage;

"use client";

import React, { useState, useEffect } from "react";
import { FaChevronDown, FaChevronRight } from "react-icons/fa";
import { Chip } from "~/app/(app)/component/chip";

// CSS for dual range slider
const rangeSliderStyles = `
  .slider-thumb::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .slider-thumb::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .slider-thumb:focus {
    outline: none;
  }

  .slider-thumb:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }
`;

interface TariffData {
  id: string;
  name: string;
  kwh: number;
  sessionFee: number;
  minChargingTime: number;
  minChargingEnergy: number;
  blockingFee: number;
  blockingFeeBeginAtMin: number;
  blockingFeeMax: number;
  kindOfTarif: string;
  validFrom: string;
  validTo: string;
  validOus: Array<{
    id: string;
    name: string;
    code: string;
  }>;
}

interface RoamingMatrixData {
  id: string;
  name: string;
  companyName: string;
  customerNumber: string;
  supplierNumber: string;
  cpo: boolean;
  noRoaming: boolean;
  ou: {
    id: string;
    name: string;
    code: string;
  } | null;
  providers: Array<{
    providerId: string;
    providerCountryId: string;
  }>;
  acTariff: TariffData | null;
  dcTariff: TariffData | null;
  validCreditTariffs: Array<{
    id: string;
    name: string;
    tarifType: string;
    powerType: string;
    sessionCredit: number;
    energyCredit: number;
    kindOfTarif: string;
    validFrom: string;
    validTo: string | null;
  }>;
}

const RoamingMatrixPage = () => {
  const [data, setData] = useState<RoamingMatrixData[]>([]);
  const [filteredData, setFilteredData] = useState<RoamingMatrixData[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedProviders, setExpandedProviders] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState({
    contactName: "",
    hasAC: "all",
    hasDC: "all",
    hasSessionFee: "all",
    hasBlockingFee: "all",
    priceRange: [0, 1] // Min and max kWh price range
  });

  useEffect(() => {
    fetchRoamingMatrixData();
  }, []);

  const fetchRoamingMatrixData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/roaming-matrix");
      if (response.ok) {
        const data = await response.json();
        setData(data);
        setFilteredData(data);
      } else {
        console.error("Failed to fetch roaming matrix data");
      }
    } catch (error) {
      console.error("Error fetching roaming matrix data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Get price range from data
  const getPriceRange = () => {
    if (data.length === 0) return { min: 0, max: 1 };

    const prices = [];
    data.forEach(contact => {
      if (contact.acTariff) prices.push(contact.acTariff.kwh);
      if (contact.dcTariff) prices.push(contact.dcTariff.kwh);
    });

    if (prices.length === 0) return { min: 0, max: 1 };

    return {
      min: Math.min(...prices),
      max: Math.max(...prices)
    };
  };

  const priceRange = getPriceRange();

  // Initialize price range filter when data loads
  useEffect(() => {
    if (data.length > 0 && filters.priceRange[0] === 0 && filters.priceRange[1] === 1) {
      setFilters(prev => ({
        ...prev,
        priceRange: [priceRange.min, priceRange.max]
      }));
    }
  }, [data, priceRange.min, priceRange.max]);

  // Filter data based on current filters
  useEffect(() => {
    let filtered = data;

    // Contact name filter (searches in both name and companyName)
    if (filters.contactName) {
      filtered = filtered.filter(contact => {
        const name = contact.name?.toLowerCase() || "";
        const companyName = contact.companyName?.toLowerCase() || "";
        const searchTerm = filters.contactName.toLowerCase();
        return name.includes(searchTerm) || companyName.includes(searchTerm);
      });
    }

    // AC tariff filter
    if (filters.hasAC !== "all") {
      filtered = filtered.filter(contact =>
        filters.hasAC === "true" ? contact.acTariff !== null : contact.acTariff === null
      );
    }

    // DC tariff filter
    if (filters.hasDC !== "all") {
      filtered = filtered.filter(contact =>
        filters.hasDC === "true" ? contact.dcTariff !== null : contact.dcTariff === null
      );
    }

    // Session fee filter (checks both AC and DC tariffs)
    if (filters.hasSessionFee !== "all") {
      filtered = filtered.filter(contact => {
        const hasSessionFee = (contact.acTariff?.sessionFee > 0) || (contact.dcTariff?.sessionFee > 0);
        return filters.hasSessionFee === "true" ? hasSessionFee : !hasSessionFee;
      });
    }

    // Blocking fee filter (checks both AC and DC tariffs)
    if (filters.hasBlockingFee !== "all") {
      filtered = filtered.filter(contact => {
        const hasBlockingFee = (contact.acTariff?.blockingFee > 0) || (contact.dcTariff?.blockingFee > 0);
        return filters.hasBlockingFee === "true" ? hasBlockingFee : !hasBlockingFee;
      });
    }

    // Price range filter (checks both AC and DC tariffs)
    filtered = filtered.filter(contact => {
      const acPrice = contact.acTariff?.kwh;
      const dcPrice = contact.dcTariff?.kwh;

      const acInRange = acPrice ? (acPrice >= filters.priceRange[0] && acPrice <= filters.priceRange[1]) : false;
      const dcInRange = dcPrice ? (dcPrice >= filters.priceRange[0] && dcPrice <= filters.priceRange[1]) : false;

      // Show contact if either AC or DC tariff is in range (or if no tariffs exist)
      return acInRange || dcInRange || (!contact.acTariff && !contact.dcTariff);
    });

    setFilteredData(filtered);
  }, [data, filters]);

  const toggleProviderExpanded = (contactId: string) => {
    const newExpanded = new Set(expandedProviders);
    if (newExpanded.has(contactId)) {
      newExpanded.delete(contactId);
    } else {
      newExpanded.add(contactId);
    }
    setExpandedProviders(newExpanded);
  };

  // CSV Export function - only exports visible data
  const exportToCSV = () => {
    const csvHeaders = [
      "Contact Name",
      "AC kWh Price",
      "AC Session Fee",
      "AC Blocking Fee Begin At Min",
      "AC Blocking Fee",
      "AC Blocking Fee Max",
      "DC kWh Price",
      "DC Session Fee",
      "DC Blocking Fee Begin At Min",
      "DC Blocking Fee",
      "DC Blocking Fee Max"
    ];

    const csvData = filteredData.map(contact => {
      const row = [
        contact.companyName || contact.name || ""
      ];

      // AC Tariff data - only add what's visible
      if (contact.acTariff) {
        row.push(contact.acTariff.kwh.toFixed(2));
        row.push(contact.acTariff.sessionFee > 0 ? contact.acTariff.sessionFee.toFixed(2) : "");
        row.push(contact.acTariff.blockingFee > 0 && contact.acTariff.blockingFeeBeginAtMin > 0 ? contact.acTariff.blockingFeeBeginAtMin.toString() : "");
        row.push(contact.acTariff.blockingFee > 0 ? contact.acTariff.blockingFee.toFixed(2) : "");
        row.push(contact.acTariff.blockingFee > 0 && contact.acTariff.blockingFeeMax > 0 ? contact.acTariff.blockingFeeMax.toFixed(2) : "");
      } else {
        row.push("", "", "", "", ""); // Empty AC tariff fields
      }

      // DC Tariff data - only add what's visible
      if (contact.dcTariff) {
        row.push(contact.dcTariff.kwh.toFixed(2));
        row.push(contact.dcTariff.sessionFee > 0 ? contact.dcTariff.sessionFee.toFixed(2) : "");
        row.push(contact.dcTariff.blockingFee > 0 && contact.dcTariff.blockingFeeBeginAtMin > 0 ? contact.dcTariff.blockingFeeBeginAtMin.toString() : "");
        row.push(contact.dcTariff.blockingFee > 0 ? contact.dcTariff.blockingFee.toFixed(2) : "");
        row.push(contact.dcTariff.blockingFee > 0 && contact.dcTariff.blockingFeeMax > 0 ? contact.dcTariff.blockingFeeMax.toFixed(2) : "");
      } else {
        row.push("", "", "", "", ""); // Empty DC tariff fields
      }

      return row;
    });

    const csvContent = [
      csvHeaders.join(","),
      ...csvData.map(row => row.map(field => `"${field}"`).join(","))
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `roaming-matrix-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const ContactCard = ({ contact }: { contact: RoamingMatrixData }) => {
    const isProviderExpanded = expandedProviders.has(contact.id);

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-4">
        {/* Contact Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {contact.companyName || contact.name || "Unbekannter Contact"}
              </h3>
              {/* Provider IDs - show first 3, then expandable */}
              {contact.providers.length > 0 && (
                <div className="flex items-center space-x-2">
                  {contact.providers.slice(0, 3).map((provider, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 rounded text-xs font-mono"
                    >
                      {provider.providerCountryId}*{provider.providerId}
                    </span>
                  ))}
                  {contact.providers.length > 3 && (
                    <button
                      onClick={() => toggleProviderExpanded(contact.id)}
                      className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      {isProviderExpanded ? (
                        <FaChevronDown className="w-3 h-3" />
                      ) : (
                        <FaChevronRight className="w-3 h-3" />
                      )}
                      <span>+{contact.providers.length - 3}</span>
                    </button>
                  )}
                </div>
              )}
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
              {contact.ou && (
                <span>OU: {contact.ou.name}</span>
              )}
            </div>
            {/* Expanded Provider IDs */}
            {isProviderExpanded && contact.providers.length > 3 && (
              <div className="mt-2 flex flex-wrap gap-2">
                {contact.providers.slice(3).map((provider, index) => (
                  <span
                    key={index + 3}
                    className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 rounded text-xs font-mono"
                  >
                    {provider.providerCountryId}*{provider.providerId}
                  </span>
                ))}
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {contact.cpo && (
              <Chip label="CPO" className="bg-purple-100 text-purple-800 text-xs" />
            )}
            {contact.noRoaming && (
              <Chip label="Kein Roaming" className="bg-red-100 text-red-800 text-xs" />
            )}
          </div>
        </div>

        {/* Tariff Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* AC Tariff */}
          <div className={`rounded-lg p-3 border-2 ${contact.acTariff
            ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-700'
            : 'bg-gray-50 border-gray-200 dark:bg-gray-700 dark:border-gray-600'
          }`}>
            <div className="flex items-center justify-center mb-3">
              <span className={`px-2 py-1 rounded text-sm font-medium ${contact.acTariff
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100'
              }`}>
                AC
              </span>
            </div>
            {contact.acTariff ? (
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">kWh:</span>
                  <span className="font-semibold">{contact.acTariff.kwh.toFixed(2)}€</span>
                </div>
                {contact.acTariff.sessionFee > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Session:</span>
                    <span className="font-semibold">{contact.acTariff.sessionFee.toFixed(2)}€</span>
                  </div>
                )}
                {contact.acTariff.blockingFee > 0 && (
                  <>
                    {contact.acTariff.blockingFeeBeginAtMin > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-300">Blockier ab:</span>
                        <span className="font-semibold">{contact.acTariff.blockingFeeBeginAtMin}min</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Blockiergebühr:</span>
                      <span className="font-semibold">{contact.acTariff.blockingFee.toFixed(2)}€/min</span>
                    </div>
                    {contact.acTariff.blockingFeeMax > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-300">Max. Blockier:</span>
                        <span className="font-semibold">{contact.acTariff.blockingFeeMax.toFixed(2)}€</span>
                      </div>
                    )}
                  </>
                )}
              </div>
            ) : (
              <div className="text-center text-gray-400 py-4">
                Kein AC-Tarif
              </div>
            )}
          </div>

          {/* DC Tariff */}
          <div className={`rounded-lg p-3 border-2 ${contact.dcTariff
            ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700'
            : 'bg-gray-50 border-gray-200 dark:bg-gray-700 dark:border-gray-600'
          }`}>
            <div className="flex items-center justify-center mb-3">
              <span className={`px-2 py-1 rounded text-sm font-medium ${contact.dcTariff
                ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100'
              }`}>
                DC
              </span>
            </div>
            {contact.dcTariff ? (
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">kWh:</span>
                  <span className="font-semibold">{contact.dcTariff.kwh.toFixed(2)}€</span>
                </div>
                {contact.dcTariff.sessionFee > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Session:</span>
                    <span className="font-semibold">{contact.dcTariff.sessionFee.toFixed(2)}€</span>
                  </div>
                )}
                {contact.dcTariff.blockingFee > 0 && (
                  <>
                    {contact.dcTariff.blockingFeeBeginAtMin > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-300">Blockier ab:</span>
                        <span className="font-semibold">{contact.dcTariff.blockingFeeBeginAtMin}min</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Blockiergebühr:</span>
                      <span className="font-semibold">{contact.dcTariff.blockingFee.toFixed(2)}€/min</span>
                    </div>
                    {contact.dcTariff.blockingFeeMax > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-300">Max. Blockier:</span>
                        <span className="font-semibold">{contact.dcTariff.blockingFeeMax.toFixed(2)}€</span>
                      </div>
                    )}
                  </>
                )}
              </div>
            ) : (
              <div className="text-center text-gray-400 py-4">
                Kein DC-Tarif
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };



  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Roaming Matrix
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Übersicht aller Contacts mit ihren gültigen zugeordneten Tarifen
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Roaming Matrix
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Übersicht aller Contacts mit ihren zugeordneten Tarifen
        </p>

        {/* Filter Section */}
        <div className="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Filter</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            {/* Contact Name Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Contact Name
              </label>
              <input
                type="text"
                value={filters.contactName}
                onChange={(e) => setFilters(prev => ({ ...prev, contactName: e.target.value }))}
                placeholder="Name oder Firma suchen..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white text-sm"
              />
            </div>

            {/* AC Tariff Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                AC-Tarif
              </label>
              <select
                value={filters.hasAC}
                onChange={(e) => setFilters(prev => ({ ...prev, hasAC: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white text-sm"
              >
                <option value="all">Alle</option>
                <option value="true">Mit AC-Tarif</option>
                <option value="false">Ohne AC-Tarif</option>
              </select>
            </div>

            {/* DC Tariff Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                DC-Tarif
              </label>
              <select
                value={filters.hasDC}
                onChange={(e) => setFilters(prev => ({ ...prev, hasDC: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white text-sm"
              >
                <option value="all">Alle</option>
                <option value="true">Mit DC-Tarif</option>
                <option value="false">Ohne DC-Tarif</option>
              </select>
            </div>

            {/* Session Fee Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Startgebühr
              </label>
              <select
                value={filters.hasSessionFee}
                onChange={(e) => setFilters(prev => ({ ...prev, hasSessionFee: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white text-sm"
              >
                <option value="all">Alle</option>
                <option value="true">Mit Startgebühr</option>
                <option value="false">Ohne Startgebühr</option>
              </select>
            </div>

            {/* Blocking Fee Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Blockiergebühr
              </label>
              <select
                value={filters.hasBlockingFee}
                onChange={(e) => setFilters(prev => ({ ...prev, hasBlockingFee: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white text-sm"
              >
                <option value="all">Alle</option>
                <option value="true">Mit Blockiergebühr</option>
                <option value="false">Ohne Blockiergebühr</option>
              </select>
            </div>

            {/* Price Range Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Energiepreis (€/kWh)
              </label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>{filters.priceRange[0].toFixed(3)}€</span>
                  <span>-</span>
                  <span>{filters.priceRange[1].toFixed(3)}€</span>
                </div>
                <div className="relative">
                  {/* Min Range Slider */}
                  <input
                    type="range"
                    min={priceRange.min}
                    max={priceRange.max}
                    step="0.001"
                    value={filters.priceRange[0]}
                    onChange={(e) => {
                      const newMin = parseFloat(e.target.value);
                      if (newMin <= filters.priceRange[1]) {
                        setFilters(prev => ({
                          ...prev,
                          priceRange: [newMin, prev.priceRange[1]]
                        }));
                      }
                    }}
                    className="absolute w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-600 slider-thumb"
                  />
                  {/* Max Range Slider */}
                  <input
                    type="range"
                    min={priceRange.min}
                    max={priceRange.max}
                    step="0.001"
                    value={filters.priceRange[1]}
                    onChange={(e) => {
                      const newMax = parseFloat(e.target.value);
                      if (newMax >= filters.priceRange[0]) {
                        setFilters(prev => ({
                          ...prev,
                          priceRange: [prev.priceRange[0], newMax]
                        }));
                      }
                    }}
                    className="absolute w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-600 slider-thumb"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Clear Filters Button */}
          <div className="mt-4">
            <button
              onClick={() => setFilters({
                contactName: "",
                hasAC: "all",
                hasDC: "all",
                hasSessionFee: "all",
                hasBlockingFee: "all",
                priceRange: [priceRange.min, priceRange.max]
              })}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Filter zurücksetzen
            </button>
          </div>
        </div>

        <div className="mt-4 flex items-center justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {filteredData.length} von {data.length} Contacts angezeigt
          </span>
          <button
            onClick={exportToCSV}
            disabled={filteredData.length === 0}
            className="px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            CSV Export ({filteredData.length} Einträge)
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {filteredData.length === 0 ? (
          <div className="col-span-full bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-8 text-center">
            <div className="text-gray-400 text-lg mb-2">🌐</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {data.length === 0 ? "Keine Contacts in der Roaming Matrix" : "Keine Contacts entsprechen den Filterkriterien"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {data.length === 0
                ? "Es wurden noch keine Contacts für die Roaming Matrix markiert. Bearbeiten Sie Contacts und setzen Sie das Flag 'Sichtbar in Roaming Matrix'."
                : "Versuchen Sie, die Filter anzupassen oder zurückzusetzen."
              }
            </p>
          </div>
        ) : (
          filteredData.map((contact) => (
            <ContactCard key={contact.id} contact={contact} />
          ))
        )}
      </div>
    </div>
  );
};

export default RoamingMatrixPage;

"use client";

import React, { useState, useEffect } from "react";
import { Chip } from "~/app/(app)/component/chip";

interface TariffData {
  id: string;
  name: string;
  kwh: number;
  sessionFee: number;
  kindOfTarif: string;
  validFrom: string;
  validTo: string;
  validOus: Array<{
    id: string;
    name: string;
    code: string;
  }>;
}

interface RoamingMatrixData {
  id: string;
  name: string;
  companyName: string;
  customerNumber: string;
  supplierNumber: string;
  cpo: boolean;
  noRoaming: boolean;
  ou: {
    id: string;
    name: string;
    code: string;
  } | null;
  acTariff: TariffData | null;
  dcTariff: TariffData | null;
  validCreditTariffs: Array<{
    id: string;
    name: string;
    tarifType: string;
    powerType: string;
    sessionCredit: number;
    energyCredit: number;
    kindOfTarif: string;
    validFrom: string;
    validTo: string | null;
  }>;
}

const RoamingMatrixPage = () => {
  const [data, setData] = useState<RoamingMatrixData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRoamingMatrixData();
  }, []);

  const fetchRoamingMatrixData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/roaming-matrix");
      if (response.ok) {
        const data = await response.json();
        setData(data);
      } else {
        console.error("Failed to fetch roaming matrix data");
      }
    } catch (error) {
      console.error("Error fetching roaming matrix data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Generate color for tariff based on tariff ID
  const getTariffColor = (tariffId: string | null, type: "AC" | "DC") => {
    if (!tariffId) return "gray";

    // Create a simple hash from tariff ID to get consistent colors
    let hash = 0;
    for (let i = 0; i < tariffId.length; i++) {
      hash = tariffId.charCodeAt(i) + ((hash << 5) - hash);
    }

    const colors = [
      "red", "blue", "green", "yellow", "purple", "pink",
      "indigo", "orange", "teal", "cyan", "lime", "amber"
    ];

    return colors[Math.abs(hash) % colors.length];
  };

  const ContactCard = ({ contact }: { contact: RoamingMatrixData }) => {
    const acColor = getTariffColor(contact.acTariff?.id || null, "AC");
    const dcColor = getTariffColor(contact.dcTariff?.id || null, "DC");

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-4">
        {/* Contact Header */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {contact.companyName || contact.name || "Unbekannter Contact"}
            </h3>
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
              {contact.customerNumber && (
                <span>Kunde: {contact.customerNumber}</span>
              )}
              {contact.ou && (
                <span>OU: {contact.ou.name}</span>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {contact.cpo && (
              <Chip label="CPO" className="bg-purple-100 text-purple-800 text-xs" />
            )}
            {contact.noRoaming && (
              <Chip label="Kein Roaming" className="bg-red-100 text-red-800 text-xs" />
            )}
          </div>
        </div>

        {/* Tariff Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* AC Tariff */}
          <div className={`rounded-lg p-3 border-2 ${contact.acTariff
            ? `bg-${acColor}-50 border-${acColor}-200 dark:bg-${acColor}-900/20 dark:border-${acColor}-700`
            : 'bg-gray-50 border-gray-200 dark:bg-gray-700 dark:border-gray-600'
          }`}>
            <div className="flex items-center justify-between mb-2">
              <span className={`px-2 py-1 rounded text-sm font-medium ${contact.acTariff
                ? `bg-${acColor}-100 text-${acColor}-800 dark:bg-${acColor}-800 dark:text-${acColor}-100`
                : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100'
              }`}>
                AC
              </span>
              {contact.acTariff && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {contact.acTariff.kindOfTarif}
                </span>
              )}
            </div>
            {contact.acTariff ? (
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  {contact.acTariff.name}
                </h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">kWh:</span>
                    <span className="font-semibold">{contact.acTariff.kwh.toFixed(4)}€</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Session:</span>
                    <span className="font-semibold">{contact.acTariff.sessionFee?.toFixed(2) || '0.00'}€</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-400 py-4">
                Kein AC-Tarif
              </div>
            )}
          </div>

          {/* DC Tariff */}
          <div className={`rounded-lg p-3 border-2 ${contact.dcTariff
            ? `bg-${dcColor}-50 border-${dcColor}-200 dark:bg-${dcColor}-900/20 dark:border-${dcColor}-700`
            : 'bg-gray-50 border-gray-200 dark:bg-gray-700 dark:border-gray-600'
          }`}>
            <div className="flex items-center justify-between mb-2">
              <span className={`px-2 py-1 rounded text-sm font-medium ${contact.dcTariff
                ? `bg-${dcColor}-100 text-${dcColor}-800 dark:bg-${dcColor}-800 dark:text-${dcColor}-100`
                : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100'
              }`}>
                DC
              </span>
              {contact.dcTariff && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {contact.dcTariff.kindOfTarif}
                </span>
              )}
            </div>
            {contact.dcTariff ? (
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  {contact.dcTariff.name}
                </h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">kWh:</span>
                    <span className="font-semibold">{contact.dcTariff.kwh.toFixed(4)}€</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Session:</span>
                    <span className="font-semibold">{contact.dcTariff.sessionFee?.toFixed(2) || '0.00'}€</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-400 py-4">
                Kein DC-Tarif
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };



  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Roaming Matrix
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Übersicht aller Contacts mit ihren gültigen zugeordneten Tarifen
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Roaming Matrix
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Übersicht aller Contacts mit ihren zugeordneten Tarifen. Gleiche Farben zeigen gleiche Tarife an.
        </p>
        <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
          <span>{data.length} Contacts gefunden</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {data.length === 0 ? (
          <div className="col-span-full bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-8 text-center">
            <div className="text-gray-400 text-lg mb-2">🌐</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Keine Contacts in der Roaming Matrix
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Es wurden noch keine Contacts für die Roaming Matrix markiert.
              Bearbeiten Sie Contacts und setzen Sie das Flag "Sichtbar in Roaming Matrix".
            </p>
          </div>
        ) : (
          data.map((contact) => (
            <ContactCard key={contact.id} contact={contact} />
          ))
        )}
      </div>
    </div>
  );
};

export default RoamingMatrixPage;

import { PrismaClient } from "@prisma/client";
import { env } from "../src/env.js";
import fs from "fs";
import { createPayoutExportFile } from "../src/utils/stripe/reportHelper.ts";
import { NextResponse } from "next/server.js";

const prisma = new PrismaClient();

async function main() {
  const payoutId = "po_1RWRTXINwl1ZJuJdXpdRkm7g";
  const fullPath = `${env.STRIPE_PAYOUT_REPORTS}/raw_report_object_${payoutId}.json`;

  // Prüfen, ob die Datei existiert
  if (fs.existsSync(fullPath)) {
    const succes = createPayoutExportFile(null, fullPath, createFile, createPayoutItems);
  } else {
    // Fehlermeldung, wenn die Datei nicht gefunden wurde
    return false;
  }
}

main();

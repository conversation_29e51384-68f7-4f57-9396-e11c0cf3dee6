import { PrismaClient } from "@prisma/client";

import fs from "fs";
import { createPayoutExportFile } from "../src/utils/stripe/reportHelper.ts";
import type { Stripe } from "stripe";

const prisma = new PrismaClient();

async function main() {
  const payoutId = "po_1RWRTXINwl1ZJuJdXpdRkm7g";
  const fullPath = `../payoutReports/raw_report_object_${payoutId}.json`;

  // Prü<PERSON>, ob die Datei existiert
  if (fs.existsSync(fullPath)) {
    const succes = createPayoutExportFile(null, fullPath, true, true);
  } else {
    // Fehlermeldung, wenn die Datei nicht gefunden wurde
    return false;
  }
}

main();
